import os



class Config:



    @classmethod
    def get_source_database(cls):
        """Get source database name from environment variable (dynamically updated)."""
        return os.getenv("SOURCE_DATABASE", "Oracle")

    @classmethod
    def get_target_database(cls):
        """Get target database name from environment variable (dynamically updated)."""
        return os.getenv("TARGET_DATABASE", "Postgres")




    # LLM Configuration
    LLM_PROVIDER=os.getenv('LLM_PROVIDER', 'azure_openai')
    # LLM_PROVIDER=gemini
    # LLM_PROVIDER=anthropic

    # Connection URL
    host = os.getenv("API_HOST","https://dvnext.qmigrator.ai")
    Connection_URL = host + "/api/v1/"
    
    Local_Path = os.path.dirname(os.path.realpath(__file__))
    Cloud_Path = '/mnt/pypod'
    
    Qbook_Path = '/mnt/qbook'
    Qbook_Local_Path = Local_Path + '/' +'qbookv2'
    
    Temp_Path = '/mnt/tmp'
    Temp_Local_Path = Local_Path + '/' + 'tmp'
    
    # QBook DB Configuration
    QBOOK_USERNAME = os.getenv("QBOOK_USERNAME", "qmigprod")
    QBOOK_PASSWORD = os.getenv("QBOOK_PASSWORD", "Qmigr@tor@97!32")
    QBOOK_DATABASE = os.getenv("QBOOK_DATABASE", "QBookv2")
    QBOOK_PORT = os.getenv("QBOOK_PORT", "5432")
    QBOOK_HOST = os.getenv("QBOOK_HOST", "qmigprod-db.postgres.database.azure.com") # qmigprod-db.postgres.database.azure.com and ********

    # Azure OpenAI Configuration
    AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://ai-testgeneration707727059630.openai.azure.com/")
    AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "wBjgqz2HegyKwtsNCInM8T0aGAYsSFQ2sPHrv2N9BNhmmreKVJ1NJQQJ99BDACYeBjFXJ3w3AAAAACOGQOtm")
    AZURE_DEPLOYMENT_NAME = os.getenv("AZURE_DEPLOYMENT_NAME", "gpt4-deployment")
    AZURE_OPENAI_MODEL = os.getenv("AZURE_OPENAI_MODEL", "gpt-4o")
    AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview")
    AZURE_OPENAI_TEMPERATURE = os.getenv("AZURE_OPENAI_TEMPERATURE", "0.1")  # Low temperature for precise SQL conversion
    AZURE_OPENAI_MAX_TOKENS = os.getenv("AZURE_OPENAI_MAX_TOKENS", "4096")


    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")
    OPENAI_TEMPERATURE = os.getenv("OPENAI_TEMPERATURE", "0.7")
    OPENAI_MAX_TOKENS = os.getenv("OPENAI_MAX_TOKENS", "4096")

    # Anthropic Configuration
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-5-sonnet-20241022")
    ANTHROPIC_TEMPERATURE = os.getenv("ANTHROPIC_TEMPERATURE", "0.7")
    ANTHROPIC_MAX_TOKENS = os.getenv("ANTHROPIC_MAX_TOKENS", "4096")

    # Groq Configuration
    GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
    GROQ_MODEL = os.getenv("GROQ_MODEL", "")
    GROQ_TEMPERATURE = os.getenv("GROQ_TEMPERATURE", "0.7")
    GROQ_MAX_TOKENS = os.getenv("GROQ_MAX_TOKENS", "4096")

    # Google Gemini Configuration
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCeBF3bPaNGaFNzJgLIpOsSeqG8S2FRmNk")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.0-flash")
    GEMINI_TEMPERATURE = os.getenv("GEMINI_TEMPERATURE", "0.7")
    GEMINI_MAX_TOKENS = os.getenv("GEMINI_MAX_TOKENS", "4096")

    # Ollama Configuration (for local models)
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "llama3")
    OLLAMA_TEMPERATURE = os.getenv("OLLAMA_TEMPERATURE", "0.7")
    OLLAMA_MAX_TOKENS = os.getenv("OLLAMA_MAX_TOKENS", "4096")

    # JWT Authentication Configuration
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "hfX.3zb3q3DYsN7o7.$z6u6IQ9BB7(@6")  # Fallback for development
    JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_EXPIRATION_HOURS = os.getenv("JWT_EXPIRATION_HOURS", "24")
    JWT_ISSUER = os.getenv("JWT_ISSUER", "https://qmigrator.ai")
    JWT_AUDIENCE = os.getenv("JWT_AUDIENCE", "")