"""
Local Main for Stage 2 Conversion Agent

This script runs the Stage 2 conversion agent locally with hardcoded values,
processing AI-corrected statements from Stage 1 through a sophisticated 9-node pipeline
to identify and update QMigrator Python modules responsible for conversion failures.
"""

import sys
import uuid
from typing import Dict, Any
from llm_config.config_manager import Config<PERSON>anager
from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder
from common.common import create_llm


def setup_stage2_application() -> Any:
    """
    Set up the Stage 2 Conversion Agent application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for Stage 2 module
    enhancement workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    QMigrator module analysis and enhancement tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for Stage 2 module enhancement operations

    Returns:
        Any: Initialized LLM instance ready for Stage 2 module enhancement workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_stage2_application()
        >>> # LLM is now ready for Stage 2 module enhancement tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for Stage 2 module enhancement...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for QMigrator module analysis")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_stage2_workflow(llm: Any, migration_name: str, process_type: str, schema_name: str, 
                       object_name: str, cloud_category: str, objecttype: str = None, 
                       source_statement: str = None, converted_statement: str = None,
                       max_attempts: int = 5) -> Dict[str, Any]:
    """
    Execute the complete Stage 2 module enhancement workflow.

    This function orchestrates the entire AI-driven Stage 2 process using a sophisticated
    9-node pipeline that includes feature identification, module enhancement, validation loops,
    and iterative improvement until successful module updates.

    Workflow Steps:
        1. Process Type Decision: Route between qmigrator/qbook processing paths
        2. Post-Stage1 Processing: Load approved statements and source code (QMigrator)
        3. Map Feature Combinations: Combine statements with features
        4. Identify Responsible Features: AI analysis of conversion failures
        5. Categorize Execution Modules: Organize modules by execution order
        6. Execute Pre Features: Run pre-processing modules
        7. Combine Driver Module: Create combined module for enhancement
        8. Enhance Driver Module: AI-driven module improvement
        9. Decompose Enhanced Module: Extract individual enhanced modules
        10. Validate Module Enhancement: Syntax and structure validation
        11. Execute Complete Pipeline: Run enhanced modules
        12. AI Statement Comparison: Verify functional equivalence
        13. Enhancement Iteration Control: Manage retry logic
        14. Statement Loop Decision: Process multiple statements
        15. Complete Processing: Finalize workflow

    Args:
        llm (Any): Initialized Language Model instance for AI-driven module analysis
        migration_name (str): Migration identifier (e.g., Oracle_Postgres14)
        process_type (str): Processing mode - "qmigrator" or "qbook"
        schema_name (str): Database schema name
        object_name (str): Database object name
        cloud_category (str): Environment category - "local" or "cloud"
        objecttype (str, optional): Object type for qmigrator mode
        source_statement (str, optional): Source statement for qbook mode
        converted_statement (str, optional): Expected converted statement for qbook mode
        max_attempts (int): Maximum enhancement attempts per statement

    Returns:
        Dict[str, Any]: Workflow execution results containing final state, completion status,
                       enhanced modules, and complete audit trail

    Example:
        >>> llm = setup_stage2_application()
        >>> result = run_stage2_workflow(llm, "Oracle_Postgres14", "qmigrator", "HR", "EMPLOYEE_PROC", "local")
        >>> print(f"Stage 2 completed: {result.get('workflow_completed', False)}")
    """
    
    print("🔧 Setting up Stage 2 conversion environment...")
    
    # Initialize the Stage 2 workflow graph builder with the LLM
    graph_builder = Stage2GraphBuilder(llm)
    graph_builder.setup_graph()
    
    # Generate workflow visualization for debugging and documentation
    graph_builder.save_graph_image(graph_builder.graph)
    
    print("🔧 Stage 2 environment setup completed successfully.")
    print(f"📊 Processing Mode: {process_type.upper()}")
    print(f"🗂️ Migration: {migration_name}")
    print(f"📁 Object: {schema_name}.{object_name}")
    print(f"☁️ Environment: {cloud_category}")

    # Create input data for Stage 2 workflow
    workflow_input = {
        "migration_name": migration_name,
        "process_type": process_type,
        "schema_name": schema_name,
        "object_name": object_name,
        "cloud_category": cloud_category.lower(),
        "max_attempts": max_attempts
    }
    
    # Add process-type specific fields
    if process_type == "qmigrator":
        if objecttype:
            workflow_input["objecttype"] = objecttype
            print(f"🔧 Object Type: {objecttype}")
    elif process_type == "qbook":
        if source_statement:
            workflow_input["source_statement"] = source_statement
            print(f"📝 Source Statement: {len(source_statement)} characters")
        if converted_statement:
            workflow_input["converted_statement"] = converted_statement
            print(f"🎯 Expected Statement: {len(converted_statement)} characters")

    # Create a unique thread ID for this workflow execution to enable state tracking
    thread_id = f"stage2_thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    try:
        print("🔄 Starting Stage 2 workflow execution...")
        
        # Execute the Stage 2 workflow
        result = graph_builder.invoke_graph(workflow_input, thread_id=thread_id)
        
        print("🎉 Stage 2 workflow completed!")
        
        # Display results summary
        if result.get("workflow_completed"):
            print("✅ All statements processed successfully!")
            
            completed_count = len(result.get("completed_statements", []))
            failed_count = len(result.get("failed_statements", []))
            total_statements = completed_count + failed_count
            
            if total_statements > 0:
                success_rate = (completed_count / total_statements * 100)
                print(f"📊 Success Rate: {success_rate:.1f}% ({completed_count}/{total_statements})")
            
            if result.get("final_excel_path"):
                print(f"📊 Excel Report: {result['final_excel_path']}")
        else:
            print("⚠️ Workflow completed with some issues")

        return result

    except Exception as e:
        print(f"❌ Stage 2 workflow failed: {str(e)}")
        raise


def main():
    """
    Main entry point for the Stage 2 Conversion Agent local execution.

    This function demonstrates the complete Stage 2 module enhancement workflow using
    sample configuration data. The inputs are hardcoded for easy local testing
    without requiring command-line arguments or user input.

    The main function:
        - Sets up the AI application with proper LLM configuration
        - Executes the complete Stage 2 workflow
        - Handles errors gracefully with detailed error reporting
        - Provides clear success/failure feedback

    Sample Configurations:
        - QMigrator mode: Object-level processing for complete database objects
        - QBook mode: Statement-level processing for individual SQL statements

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        print("🚀 Starting Stage 2 Conversion Agent Local Execution...")
        print("=" * 70)
        
        # Setup the application
        llm = setup_stage2_application()
        
        # Configuration for Stage 2 workflow
        # You can modify these values to test different scenarios
        
        # === QMigrator Mode Configuration ===
        # Uncomment this section to test QMigrator mode (object-level processing)
        migration_name = "Oracle_Postgres14"
        process_type = "qmigrator"  # or "qbook"
        schema_name = "HR"
        object_name = "EMPLOYEE_PROC"
        cloud_category = "local"  # or "cloud"
        objecttype = "Procedure"  # Required for qmigrator mode
        max_attempts = 5
        
        print("📊 Configuration:")
        print(f"   - Migration: {migration_name}")
        print(f"   - Process Type: {process_type}")
        print(f"   - Schema: {schema_name}")
        print(f"   - Object: {object_name}")
        print(f"   - Environment: {cloud_category}")
        print(f"   - Object Type: {objecttype}")
        print(f"   - Max Attempts: {max_attempts}")
        print()
        
        # Run the Stage 2 workflow
        print("🔄 Starting Stage 2 workflow...")
        result = run_stage2_workflow(
            llm=llm,
            migration_name=migration_name,
            process_type=process_type,
            schema_name=schema_name,
            object_name=object_name,
            cloud_category=cloud_category,
            objecttype=objecttype,
            max_attempts=max_attempts
        )

        print("\n🎉 Stage 2 workflow completed successfully!")
        print("📋 Check the generated Excel reports for detailed analysis")
        
        # === QBook Mode Example (Commented) ===
        # Uncomment this section to test QBook mode (statement-level processing)
        """
        # QBook mode configuration
        migration_name = "Oracle_Postgres14"
        process_type = "qbook"
        schema_name = "HR"
        object_name = "TEST_STATEMENT"
        cloud_category = "local"
        source_statement = "SELECT emp_id, emp_name FROM employees WHERE dept_id = p_dept_id"
        converted_statement = "SELECT emp_id, emp_name FROM employees WHERE dept_id = $1"
        max_attempts = 5
        
        result = run_stage2_workflow(
            llm=llm,
            migration_name=migration_name,
            process_type=process_type,
            schema_name=schema_name,
            object_name=object_name,
            cloud_category=cloud_category,
            source_statement=source_statement,
            converted_statement=converted_statement,
            max_attempts=max_attempts
        )
        """

    except ValueError as e:
        print(f"\n❌ Configuration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
