from Conversion_Agent_Stage2.state.state import (
    Stage2WorkflowState,
    ResponsibleFeature,
    ResponsibleFeaturesAnalysisOutput,
    ModuleEnhancementOutput,
    TransformationChange,
    ParameterMapping,
    TransformationGuidance,
    StatementComparisonOutput
)

__all__ = [
    "Stage2WorkflowState",
    "ResponsibleFeature",
    "ResponsibleFeaturesAnalysisOutput",
    "FeatureValidationResult",
    "IdentifiedFeaturesValidationOutput",
    "ModuleEnhancementOutput",
    "TransformationChange",
    "ParameterMapping",
    "TransformationGuidance",
    "StatementComparisonOutput"
]