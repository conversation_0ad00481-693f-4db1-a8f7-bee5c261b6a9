"""
Streamlit Application for Stage 2 Conversion Agent

This application provides a user-friendly interface for the Stage 2 Conversion Agent
which processes AI-corrected statements from Stage 1 through a streamlined 9-node pipeline
for comprehensive QMigrator module enhancement.
"""

import streamlit as st
import uuid
from llm_config.config_manager import ConfigManager
from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder
from common.common import create_llm

# Page configuration
st.set_page_config(
    page_title="🔧 Stage 2 Conversion Agent",
    page_icon="🔧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2e7d32;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1565c0;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        color: #0d47a1;
        margin: 1rem 0;
    }
    .warning-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = None
    if 'workflow_history' not in st.session_state:
        st.session_state.workflow_history = []


def create_stage2_agent():
    """Create and initialize the Stage 2 conversion agent."""
    try:
        # Setup the LLM
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        llm = create_llm(llm_provider, config_manager)
        st.success(f"✅ LLM initialized: {llm_provider}")

        # Initialize Stage 2 graph builder
        graph_builder = Stage2GraphBuilder(llm)
        st.info("🔧 Building Stage 2 workflow graph...")

        # Setup the graph (this compiles the workflow)
        graph_builder.setup_graph()
        st.success("✅ Stage 2 workflow graph compiled successfully")

        return graph_builder, llm
    except Exception as e:
        st.error(f"❌ Failed to initialize Stage 2 Conversion Agent: {str(e)}")
        st.error(f"🔍 Error details: {type(e).__name__}")

        # Show more detailed error information
        import traceback
        with st.expander("🔧 Detailed Error Information", expanded=False):
            st.code(traceback.format_exc())

        return None, None


def display_header():
    """Display the application header."""
    st.markdown('<div class="main-header">🔧 Stage 2 Conversion Agent</div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Information about Stage 2
    st.markdown("""
    <div class="info-box">
        <h4>🎯 About Stage 2 Conversion Agent</h4>
        <p>Stage 2 processes AI-corrected statements from Stage 1 through a sophisticated 9-node pipeline
        to identify and update QMigrator Python modules responsible for conversion failures.</p>
    </div>
    """, unsafe_allow_html=True)


def display_sidebar():
    """Display the sidebar with configuration."""
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Display current LLM provider
        try:
            config_manager = ConfigManager()
            current_provider = config_manager.get_llm_provider()
            st.info(f"🤖 Current LLM: **{current_provider.upper()}**")

            # Show additional config info
            st.markdown("### ⚙️ System Status")
            st.success("✅ LLM Configuration: Active")
            st.success("✅ Stage 2 Pipeline: Ready")
            st.success("✅ Graph Builder: Initialized")

        except Exception as e:
            st.warning("⚠️ Could not load LLM configuration")
            st.error(f"Error: {str(e)}")

        # Environment info
        st.markdown("### 🌍 Environment")
        try:
            from config import Config
            st.text(f"📁 QBook Path: {getattr(Config, 'Qbook_Path', 'Not configured')}")
            st.text(f"📁 Temp Path: {getattr(Config, 'Temp_Path', 'Not configured')}")
        except:
            st.text("📁 Paths: Configuration not available")

        st.markdown("---")
        
        # Stage 2 Workflow Information
        st.markdown("""
        ### 🔄 9-Node Pipeline

        **Core Nodes (1-5):**
        1. 🔀 Process Type Decision
        2. 📁 Post Stage1 Processing
        3. 🔗 Map Feature Combinations
        4. 📝 Statement Level Processing
        5. 🔍 Identify Responsible Features

        **Enhancement Pipeline (6-14):**
        6. 📋 Categorize Execution Modules
        7. 🔄 Execute Pre Features
        8. 🤖 Combine Driver Module
        9. 🔧 Enhance Driver Module
        10. 📦 Decompose Enhanced Module
        11. ✅ Validate Module Enhancement
        12. ⚡ Execute Complete Pipeline
        13. 🧠 AI Statement Comparison
        14. 🔄 Enhancement Iteration Control

        **Control Nodes:**
        - 🔄 Statement Loop Decision
        - ✨ Complete Processing
        """)

        # Workflow status
        if st.session_state.workflow_running:
            st.warning("🔄 Workflow Running...")

        # Workflow history
        if st.session_state.workflow_history:
            st.markdown("### 📊 Recent Executions")
            for i, execution in enumerate(st.session_state.workflow_history[-3:], 1):
                status_icon = "✅" if execution['success'] else "❌"
                st.text(f"{status_icon} {execution['timestamp']}")
                st.text(f"   {execution['migration']} | {execution['mode']}")
                st.text(f"   {execution['object']}")
                st.markdown("---")


def display_workflow_results(result):
    """Display detailed workflow results."""
    if not result:
        return

    st.markdown('<div class="section-header">📊 Workflow Results</div>', unsafe_allow_html=True)

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Workflow Status",
            "✅ Completed" if result.get("workflow_completed") else "❌ Failed",
            delta=None
        )

    with col2:
        completed_count = len(result.get("completed_statements", []))
        st.metric("Completed Statements", completed_count)

    with col3:
        failed_count = len(result.get("failed_statements", []))
        st.metric("Failed Statements", failed_count)

    with col4:
        total_statements = completed_count + failed_count
        success_rate = (completed_count / total_statements * 100) if total_statements > 0 else 0
        st.metric("Success Rate", f"{success_rate:.1f}%")

    # Detailed results
    if result.get("final_excel_path"):
        st.markdown("""
        <div class="success-box">
            <h4>📊 Excel Report Generated</h4>
            <p><strong>Path:</strong> {}</p>
            <p>The Excel file contains detailed logs of the entire workflow including:</p>
            <ul>
                <li>Pipeline Overview with statement-by-statement progress</li>
                <li>Module Processing details with code changes</li>
                <li>Enhancement Feedback with AI analysis</li>
            </ul>
        </div>
        """.format(result["final_excel_path"]), unsafe_allow_html=True)

    # Show completed statements
    if result.get("completed_statements"):
        with st.expander("✅ Successfully Processed Statements", expanded=False):
            for i, stmt in enumerate(result["completed_statements"], 1):
                st.write(f"**Statement {i}:**")
                st.code(stmt.get("final_output", "No output available"), language="sql")

    # Show failed statements
    if result.get("failed_statements"):
        with st.expander("❌ Failed Statements", expanded=False):
            for i, stmt in enumerate(result["failed_statements"], 1):
                st.write(f"**Failed Statement {i}:**")
                st.write(f"**Reason:** {stmt.get('failure_reason', 'Unknown')}")
                if stmt.get("original_statement"):
                    st.code(stmt["original_statement"], language="sql")


def process_stage2_workflow(migration_name, process_type, schema_name, object_name,
                           cloud_category, objecttype, source_statement, converted_statement,
                           max_attempts, graph_builder):
    """Process the Stage 2 workflow using the graph builder."""
    import datetime

    try:
        with st.spinner("🔄 Initializing Stage 2 workflow..."):
            # Create input data for Stage 2 workflow
            workflow_input = {
                "migration_name": migration_name,
                "process_type": process_type,
                "schema_name": schema_name,
                "object_name": object_name,
                "cloud_category": cloud_category.lower(),
                "max_attempts": max_attempts
            }

            # Add process-type specific fields
            if process_type == "qmigrator":
                if objecttype:
                    workflow_input["objecttype"] = objecttype
            elif process_type == "qbook":
                if source_statement:
                    workflow_input["source_statement"] = source_statement
                if converted_statement:
                    workflow_input["converted_statement"] = converted_statement

            st.success("🔧 Stage 2 workflow initialized successfully.")
            st.info(f"📊 Processing Mode: **{process_type.upper()}**")
            st.info(f"🗂️ Migration: **{migration_name}**")
            st.info(f"📁 Object: **{schema_name}.{object_name}**")

        with st.spinner("🔄 Executing Stage 2 workflow..."):
            # Create a unique thread ID for this workflow execution
            thread_id = f"stage2_thread_{uuid.uuid4()}"
            st.info(f"🔗 Using thread ID: {thread_id}")

            try:
                # Execute the Stage 2 workflow
                result = graph_builder.invoke_graph(workflow_input, thread_id=thread_id)

                st.success("🎉 Stage 2 workflow completed!")

                # Store results in session state
                st.session_state.workflow_results = result

                # Add to workflow history
                execution_record = {
                    'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'migration': migration_name,
                    'mode': process_type.upper(),
                    'object': f"{schema_name}.{object_name}",
                    'success': result.get("workflow_completed", False)
                }
                st.session_state.workflow_history.append(execution_record)

                # Display detailed results
                display_workflow_results(result)

                return True, "🎉 Stage 2 execution completed successfully!"

            except Exception as e:
                st.error(f"❌ Stage 2 workflow failed: {str(e)}")

                # Add failed execution to history
                execution_record = {
                    'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'migration': migration_name,
                    'mode': process_type.upper(),
                    'object': f"{schema_name}.{object_name}",
                    'success': False
                }
                st.session_state.workflow_history.append(execution_record)
                raise

    except Exception as e:
        return False, f"Stage 2 Error: {str(e)}"


def main():
    """Main application function."""
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar
    display_sidebar()
    
    # Initialize Stage 2 agent
    with st.spinner("🔄 Initializing Stage 2 Conversion Agent..."):
        graph_builder, _ = create_stage2_agent()

    if not graph_builder:
        st.error("❌ Cannot proceed without a valid Stage 2 Conversion Agent. Please check your configuration.")

        # Show troubleshooting information
        with st.expander("🔧 Troubleshooting Steps", expanded=True):
            st.markdown("""
            **Common Issues:**

            1. **Missing Dependencies:**
               ```bash
               pip install langchain langgraph streamlit
               ```

            2. **LLM Configuration:**
               - Check `llm_config/config_manager.py`
               - Verify API keys are set correctly
               - Ensure LLM provider is properly configured

            3. **Import Issues:**
               - Verify all Stage 2 modules are in the correct location
               - Check Python path includes the project directory
               - Ensure all required files are present

            4. **Environment Setup:**
               - Check `config.py` for proper paths
               - Verify database connection settings
               - Ensure all environment variables are set
            """)
        return

    # Show workflow monitoring if results exist
    if st.session_state.workflow_results:
        st.markdown('<div class="section-header">📊 Latest Workflow Results</div>', unsafe_allow_html=True)
        display_workflow_results(st.session_state.workflow_results)

        if st.button("🗑️ Clear Results"):
            st.session_state.workflow_results = None
            st.rerun()

        st.markdown("---")

    # Main input form
    st.markdown('<div class="section-header">📝 Stage 2 Input Configuration</div>', unsafe_allow_html=True)
    
    with st.form("stage2_form"):
        # Core Configuration
        st.subheader("🔧 Core Configuration")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            migration_name = st.text_input(
                "Migration Name:",
                value="Oracle_Postgres14",
                help="Enter the migration name (e.g., Oracle_Postgres14, SQLServer_Postgres14)"
            )
        
        with col2:
            process_type = st.selectbox(
                "Processing Mode:",
                options=["qmigrator", "qbook"],
                index=0,
                help="qmigrator: Object-level processing | qbook: Statement-level processing"
            )
        
        with col3:
            cloud_category = st.selectbox(
                "Environment:",
                options=["Local", "Cloud"],
                index=0,
                help="Select deployment environment"
            )
        
        # Object Information
        st.subheader("📁 Object Information")
        
        col1, col2 = st.columns(2)
        
        with col1:
            schema_name = st.text_input(
                "Schema Name:",
                value="HR",
                help="Database schema name"
            )
        
        with col2:
            object_name = st.text_input(
                "Object Name:",
                value="EMPLOYEE_PROC",
                help="Name of the database object"
            )

        # Process-specific fields
        if process_type == "qmigrator":
            st.subheader("🔧 QMigrator Mode Settings")
            st.markdown("""
            <div class="info-box">
                <strong>QMigrator Mode:</strong> Processes complete database objects from Stage 1 approved statements.
                Requires Stage 1 metadata files (approved_statements.csv, source_code.sql) to be present.
            </div>
            """, unsafe_allow_html=True)

            objecttype = st.text_input(
                "Object Type:",
                value="Procedure",
                help="Type of the database object (e.g., Procedure, Function, Trigger, View, Table)"
            )
            source_statement = None
            converted_statement = None
        else:
            st.subheader("📝 QBook Mode Settings")
            st.markdown("""
            <div class="info-box">
                <strong>QBook Mode:</strong> Processes individual SQL statements for targeted module enhancement.
                Useful for testing specific conversion scenarios.
            </div>
            """, unsafe_allow_html=True)

            objecttype = None

            col1, col2 = st.columns(2)

            with col1:
                source_statement = st.text_area(
                    "Source Statement:",
                    height=150,
                    placeholder="Example:\nSELECT emp_id, emp_name \nFROM employees \nWHERE dept_id = p_dept_id",
                    help="Individual source SQL statement for processing"
                )

            with col2:
                converted_statement = st.text_area(
                    "Expected Converted Statement:",
                    height=150,
                    placeholder="Example:\nSELECT emp_id, emp_name \nFROM employees \nWHERE dept_id = $1",
                    help="Expected converted SQL statement (AI-corrected target)"
                )

        # Advanced Settings
        st.subheader("⚙️ Advanced Settings")
        
        col1, col2 = st.columns([1, 3])
        
        with col1:
            max_attempts = st.number_input(
                "Max Attempts per Statement:",
                min_value=1,
                max_value=10,
                value=5,
                step=1,
                help="Maximum enhancement attempts per statement"
            )
        
        with col2:
            st.markdown("""
            <div style="margin-top: 25px; padding: 10px; background-color: #f0f2f6; border-radius: 5px;">
                <small><strong>ℹ️ Stage 2 Processing:</strong><br>
                • Identifies Python modules responsible for conversion failures<br>
                • Enhances modules through AI-driven analysis and feedback loops<br>
                • Validates enhanced modules through comprehensive testing<br>
                • Generates detailed Excel reports with audit trails</small>
            </div>
            """, unsafe_allow_html=True)

        # Submit button
        submitted = st.form_submit_button("🚀 Start Stage 2 Processing", use_container_width=True)
    
    # Help section
    with st.expander("❓ Help & Examples", expanded=False):
        st.markdown("""
        ### 🔧 QMigrator Mode
        **Prerequisites:**
        - Stage 1 must be completed for the specified object
        - Files required: `approved_statements.csv`, `source_code.sql`
        - Location: `Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/`

        **Example Configuration:**
        - Migration: Oracle_Postgres14
        - Schema: HR
        - Object: EMPLOYEE_PROC
        - Object Type: procedure

        ### 📝 QBook Mode
        **Use Cases:**
        - Testing specific SQL statement conversions
        - Debugging individual conversion issues
        - Rapid prototyping of module enhancements

        **Example Statements:**
        ```sql
        -- Source (Oracle)
        SELECT emp_id, emp_name
        FROM employees
        WHERE dept_id = p_dept_id

        -- Expected Target (PostgreSQL)
        SELECT emp_id, emp_name
        FROM employees
        WHERE dept_id = $1
        ```

        ### 🔄 Workflow Process
        1. **Feature Identification:** AI analyzes conversion failures
        2. **Module Enhancement:** Updates Python modules with improved logic
        3. **Validation:** Tests enhanced modules through multiple loops
        4. **Comparison:** Verifies functional equivalence with AI analysis
        5. **Iteration:** Repeats until success or max attempts reached
        """)

    # Process workflow when form is submitted
    if submitted:
        # Enhanced validation with helpful messages
        validation_errors = []

        if not schema_name.strip():
            validation_errors.append("❌ **Schema Name** is required")
        elif not schema_name.replace("_", "").replace("-", "").isalnum():
            validation_errors.append("❌ **Schema Name** should contain only letters, numbers, underscores, and hyphens")

        if not object_name.strip():
            validation_errors.append("❌ **Object Name** is required")
        elif not object_name.replace("_", "").replace("-", "").isalnum():
            validation_errors.append("❌ **Object Name** should contain only letters, numbers, underscores, and hyphens")

        if process_type == "qbook":
            if not source_statement.strip():
                validation_errors.append("❌ **Source Statement** is required for QBook mode")
            elif len(source_statement.strip()) < 10:
                validation_errors.append("❌ **Source Statement** seems too short (minimum 10 characters)")

            if not converted_statement.strip():
                validation_errors.append("❌ **Expected Converted Statement** is required for QBook mode")
            elif len(converted_statement.strip()) < 10:
                validation_errors.append("❌ **Expected Converted Statement** seems too short (minimum 10 characters)")

        if validation_errors:
            st.markdown("""
            <div class="error-box">
                <h4>⚠️ Please fix the following issues:</h4>
                {}
            </div>
            """.format("<br>".join(validation_errors)), unsafe_allow_html=True)
        else:
            # Set workflow running state
            st.session_state.workflow_running = True
            
            # Process the Stage 2 workflow
            success, message = process_stage2_workflow(
                migration_name,
                process_type,
                schema_name,
                object_name,
                cloud_category,
                objecttype,
                source_statement,
                converted_statement,
                max_attempts,
                graph_builder
            )

            # Reset workflow running state
            st.session_state.workflow_running = False

            if success:
                st.markdown(f'<div class="success-box">✅ {message}</div>', unsafe_allow_html=True)

                # Action buttons
                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button("🔄 Start New Processing", use_container_width=True):
                        st.session_state.workflow_results = None
                        st.rerun()

                with col2:
                    if st.button("📊 View Results", use_container_width=True):
                        if st.session_state.workflow_results:
                            st.rerun()

                with col3:
                    if st.button("📋 Download Report", use_container_width=True):
                        if st.session_state.workflow_results and st.session_state.workflow_results.get("final_excel_path"):
                            st.info(f"📁 Excel report available at: {st.session_state.workflow_results['final_excel_path']}")
                        else:
                            st.warning("No Excel report available")

            else:
                st.markdown(f'<div class="error-box">❌ {message}</div>', unsafe_allow_html=True)

                # Troubleshooting help
                with st.expander("🔧 Troubleshooting", expanded=False):
                    st.markdown("""
                    **Common Issues:**

                    1. **Missing Stage 1 Files (QMigrator mode):**
                       - Ensure Stage 1 processing completed successfully
                       - Check for `approved_statements.csv` and `source_code.sql`
                       - Verify correct migration name and object details

                    2. **Configuration Errors:**
                       - Verify LLM configuration in config files
                       - Check database connection settings
                       - Ensure proper environment variables

                    3. **Processing Failures:**
                       - Review input statements for syntax errors
                       - Check migration type matches source/target databases
                       - Verify object type is correct

                    **Need Help?**
                    - Check the workflow logs for detailed error messages
                    - Verify all prerequisites are met
                    - Contact support with the error details
                    """)

    # Footer with additional information
    st.markdown("---")

    # Statistics
    if st.session_state.workflow_history:
        total_executions = len(st.session_state.workflow_history)
        successful_executions = sum(1 for exec in st.session_state.workflow_history if exec['success'])
        success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Executions", total_executions)
        with col2:
            st.metric("Successful", successful_executions)
        with col3:
            st.metric("Success Rate", f"{success_rate:.1f}%")

    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem; margin-top: 2rem;">
        🔧 Stage 2 Conversion Agent | QMigrator Module Enhancement | Powered by AI<br>
        <small>Advanced 9-Node Pipeline for Intelligent Module Updates</small>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
