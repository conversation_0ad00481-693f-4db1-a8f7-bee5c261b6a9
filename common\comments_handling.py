import re
import numpy as np


def retrieve_singlequote_data(source_data):
    single_quote_comment_dictionary = {}

    arrow_comments = re.findall(r"\-+\>", source_data)
    for comment in arrow_comments:
        source_data = source_data.replace(comment, 'arrow_quad_marker', 1)

    singlequote_data = re.findall(r"\'.*?\'", source_data, flags=re.DOTALL)

    if len(singlequote_data):
        counter = 0
        for i in singlequote_data:
            if i.strip().lower() in source_data.lower().strip():
                unique_marker = 'single_quote_quad_marker_' + str(counter) + '_us'
                source_data = source_data.replace(i, unique_marker, 1)
                single_quote_comment_dictionary[unique_marker] = i
            counter = counter + 1
    else:
        source_data = source_data
    return source_data, single_quote_comment_dictionary


def retrieve_comments_with_rules(source_data, comment_identifiers):
    source_data = re.sub(r'\-\-+', '--', source_data)
    source_data = source_data.replace('--','\n--')

    comments_dictionary = {}
    if type(comment_identifiers) is not float and not isinstance(comment_identifiers,np.float64):
        comment_identifiers = re.sub(' +', ' ', comment_identifiers).lower()
        current_comment_identifiers_split_list = comment_identifiers.split('&')

        counter = 0
        for comment_rule in current_comment_identifiers_split_list:
            start_key = comment_rule.split('end:')[0].split('start:')[1].strip()
            end_key = comment_rule.split('end:')[1].strip()

            if '-' in start_key:
                start_key = start_key.replace('-', '\-')
            elif '/' in start_key or '*' in start_key:
                start_key = start_key.replace('/', '\/').replace('*', '\*')
            if '*/' in end_key:
                end_key = end_key.replace('*/', '\*/')
            comments_data_list = re.findall(rf"{start_key}[\s\S]*?{end_key}", source_data)

            for comments_data in comments_data_list:
                if '-' in start_key:
                    source_data = source_data.replace(comments_data,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data
                else:
                    comments_data_modified = '/*' + comments_data.replace('/*', '').replace('*/',
                                                                                            '').replace('/',
                                                                                                        '') + '*/'
                    source_data = source_data.replace(comments_data, comments_data_modified)
                    source_data = source_data.replace(comments_data_modified,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data_modified
                counter += 1
    return source_data, comments_dictionary


def replace_comment_markers(data, comment_dictionary):
    if len(comment_dictionary):
        for key, value in comment_dictionary.items():
            try:
                value = value.replace('\\', 'backslash_quad_marker')
                data = re.sub(rf'\b{key}\b', value + '\n', data, flags=re.IGNORECASE | re.DOTALL)
                data = data.replace('backslash_quad_marker', '\\')
            except Exception as e:
                print(f'Error in replace_comment_markers at {key} : {str(e)}')
                data = data.replace(key, value + '\n', 1)
    else:
        data = data
    return data


def replace_single_quote_markers(data, single_quote_comment_dictionary):
    if len(single_quote_comment_dictionary):
        # print(single_quote_comment_dictionary)

        for key, value in single_quote_comment_dictionary.items():
            try:
                # data = data.replace(key, value, 1)
                # value = re.escape(value)
                # if re.search(r'\(|\)|\'|\"|\]|\[',value,flags=re.DOTALL):
                #     data = re.sub(f'{re.escape(key)}', re.escape(value), data, flags=re.IGNORECASE | re.DOTALL)
                # else:
                data = re.sub(f'{re.escape(key)}', value, data, flags=re.IGNORECASE | re.DOTALL)
                # data = re.sub(rf'{key}', value, data, flags=re.IGNORECASE | re.DOTALL)
            except Exception as e:
                print(f'Error in replace_single_quote_markers at {key} : {str(e)}')
    else:
        data = data
    return data


def remove_comments_for_comparison(statement: str) -> str:
    """Remove all comments and comment markers for clean AI comparison only."""
    try:
        if not statement:
            return statement

        # Remove comment markers (comment_quad_marker_X_us patterns)
        statement_clean = re.sub(r'comment_quad_marker_\d+_us', '', statement)

        # Remove actual comments that may have been restored
        
        # Remove /* */ comments (multi-line)
        statement_clean = re.sub(r'/\*.*?\*/', '', statement_clean, flags=re.DOTALL)
        # Remove -- comments (single line)
        statement_clean = re.sub(r'--.*?(?=\n|$)', '', statement_clean, flags=re.MULTILINE)

        # Normalize whitespace
        statement_clean = re.sub(r'\s+', ' ', statement_clean).strip()

        return statement_clean

    except Exception as e:
        print(f"⚠️ Warning: Comment removal for comparison failed: {str(e)}")
        return statement  # Return original on error