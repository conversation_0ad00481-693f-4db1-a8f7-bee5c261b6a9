"""
Prompts for identifying responsible features in Stage 2 conversion analysis.
"""
from typing import Dict, List, Any, Optional
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_responsible_features_identification_prompt(
    original_source: str,
    ai_converted: str,
    actual_target: str,
    deployment_error: str,
    decrypted_modules: Dict[str, str],
    keyword_mapping: List[Dict[str, Any]],
    available_features: List[tuple],
    validation_feedback: Optional[str] = None,
    db_terms: Optional[Dict[str, str]] = None
) -> str:
    """
    Create AI analysis prompt for identifying responsible modules (Stage 2 style with dynamic database names).

    Args:
        original_source: Original source database statement
        ai_converted: Expected target database statement from AI
        actual_target: Actual wrong target database statement
        deployment_error: Error message from deployment
        decrypted_modules: Dictionary of decrypted Python module contents
        keyword_mapping: Keyword-to-module mapping from CSV
        available_features: List of available feature tuples
        validation_feedback: Optional feedback from previous validation attempts
        db_terms: Database-specific terms from migration name (Request-First Approach)

    Returns:
        Formatted prompt string for AI analysis with structured output
    """

    # Use provided database terms or fallback to config-based approach
    if db_terms is None:
        db_terms = get_database_specific_terms()

    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    # Format available features for prompt
    available_features_str = "\n".join([f"- {name}: {path}" for name, path in available_features])

    # Include validation feedback if available
    feedback_section = ""
    if validation_feedback:
        feedback_section = f"""
VALIDATION FEEDBACK FROM PREVIOUS ATTEMPT:
=========================================
{validation_feedback}

Use this feedback to improve your identification accuracy. Focus on the specific guidance provided.

"""

    prompt = f"""
You are a {expert_title}. Your task is to identify which specific Python modules from the available features are responsible for {migration_direction} conversion failures.

{feedback_section}CONVERSION ANALYSIS:
===================

Original {source_db} Statement:
{original_source}

AI Corrected {target_db} Statement (Expected Output):
{ai_converted}

QMigrator {target_db} Statement (Current Output):
{actual_target}

Deployment Error:
{deployment_error}

AVAILABLE FEATURES TO ANALYZE:
=============================
{available_features_str}

DECRYPTED MODULE CODE:
=====================
"""
    
    for module_name, module_code in decrypted_modules.items():
        prompt += f"\n--- {module_name.upper()} MODULE ---\n{module_code}\n"

    prompt += f"""

KEYWORD MAPPING REFERENCE:
=========================
{keyword_mapping}

ANALYSIS TASK:
=============
1. **PRIMARY GOAL**: Identify which QMigrator modules failed to produce the AI-corrected output and must transform current output to match the exact AI-corrected format
2. **COMPREHENSIVE COVERAGE**: Identify modules for ALL transformation gaps, not just deployment errors - ensure complete transformation to AI-corrected format
3. **SPECIFIC RESPONSIBILITY**: Each identified module should receive specific transformation patterns for its particular issue, not generic patterns for all issues
2. **TARGET-DRIVEN APPROACH**: Use the AI Corrected {target_db} statement as the definitive target format that modules must achieve
3. **CRITICAL**: Analyze the Deployment Error to understand the exact failure cause and impact
4. Compare AI Corrected {target_db} vs QMigrator {target_db} to identify specific transformation gaps
5. Review each available feature's decrypted code to understand conversion logic
6. Focus on modules that should transform current output to match the exact AI-corrected format
7. **ESSENTIAL**: Link each responsible module directly to the specific deployment error
8. **TRANSFORMATION GUIDANCE**: Provide specific patterns to transform current output into the exact AI-corrected format:
   - Include transformation patterns based on the exact AI-corrected output format
   - Focus on "transform current output into this exact AI-corrected format"
   - Account for whitespace, case, and formatting variations to match AI-corrected output
   - Provide step-by-step implementation guidance to achieve the AI-corrected format
9. **MODULE-SPECIFIC PATTERNS**: Provide each responsible module with transformation patterns specific to its identified issue only:
   - Each module gets patterns for its specific transformation responsibility
   - Avoid giving all modules the same generic transformation guidance
   - Focus each module's patterns on its particular part of the overall transformation
10. **MULTIPLE ISSUE HANDLING**: If multiple transformation gaps exist, identify additional modules for each remaining gap:
    - Primary: Identify deployment error modules (highest priority)
    - Secondary: Identify modules for remaining functional differences to achieve complete AI-corrected format
    - Ensure ALL transformation gaps are covered by responsible modules

IDENTIFICATION METHODOLOGY:
==========================
1. **Error Analysis**: Examine deployment error for specific syntax/keyword failures and root cause
2. **Statement Comparison**: Compare AI Corrected vs QMigrator output for differences
3. **Module Mapping**: Match {source_db} keywords OR functional patterns to responsible conversion modules
4. **Code Review**: Analyze module code to understand conversion logic and potential failures
5. **Pattern Analysis**: Identify modules that handle similar transformations even with different keywords
6. **Error-Module Linking**: Establish connection between deployment error and module responsibility (exact or functional match)

IDENTIFICATION CRITERIA:
=======================
**PRIMARY RULE**: Identify modules for deployment errors AND additional modules for remaining transformation gaps to achieve complete AI-corrected format.

**IDENTIFICATION PRIORITY**:
1. **DEPLOYMENT ERROR MODULES** (Highest Priority): Modules directly responsible for deployment errors
2. **TRANSFORMATION GAP MODULES** (High Priority): Additional modules needed for remaining differences to match AI-corrected output

**DEPLOYMENT ERROR CONDITIONS** (for primary modules):
1. Module handles keywords mentioned in deployment error OR similar functional patterns
2. Module's primary purpose is converting the failing syntax element or related variations
3. Module can address the deployment error even if exact keywords don't match

**TRANSFORMATION GAP CONDITIONS** (for additional modules):
1. Module can handle specific differences between current output and AI-corrected format
2. Module's functionality aligns with the required transformation pattern or similar patterns
3. Module is needed to achieve complete AI-corrected output structure
4. Module handles functional equivalents or variations of the required transformation
3. Module's current code lacks logic for this specific error scenario
4. Module is responsible for the specific transformation that failed
5. **CRITICAL**: Module's failure directly caused the deployment error

**EXCLUSION RULES**:
- Do NOT identify modules just because they appear in the statement
- Do NOT identify modules that work correctly but happen to be present
- Do NOT identify modules whose primary purpose differs from the error cause

**TRANSFORMATION PATTERN REQUIREMENTS**:
For each identified responsible module, provide comprehensive transformation guidance to match the AI-corrected output:
- **Target-Specific Patterns**: Show how to transform current output to match the exact AI-corrected format
- **Format-Based Matching**: Include patterns that transform current format to AI-corrected format structure
- **Implementation Steps**: Provide clear step-by-step guidance for achieving the AI-corrected output format
- **Variation Coverage**: Account for different formatting scenarios while targeting the AI-corrected format
- **AI-Corrected Output Focus**: Make all patterns specifically aimed at matching the AI-corrected output

OUTPUT FORMAT (JSON):
====================
{{
  "responsible_features": [
    {{
      "feature_name": "<feature_name>",
      "module_path": "<relative_path>",
      "responsibility_reason": "<DEPLOYMENT_ERROR_CONTEXT: Quote the specific deployment error OR state 'No deployment error - functional difference identified'> <ERROR_CAUSE: Explain why this error/difference occurred> <MODULE_ROLE: How this module should handle the transformation> <SPECIFIC_ISSUE: The exact issue this module should fix (not all issues in the statement)> <EXPECTED_FIX: What the module should do to transform its specific issue to match the AI-corrected format> <TRANSFORMATION_PATTERNS: Provide specific patterns for this module's issue only, not all transformation patterns> <IMPLEMENTATION_GUIDANCE: Step-by-step approach to fix this module's specific issue>",
      "error_impact": "High|Medium|Low",
      "keywords_matched": ["<keyword1>", "<keyword2>"],
      "confidence_score": <float between 0.0 and 1.0>
    }}
  ],
  "analysis_summary": "<comprehensive explanation covering ALL responsible features, analysis process, comparison findings, and detailed reasoning>"
}}

**RESPONSIBILITY_REASON FORMAT REQUIREMENTS**:
The responsibility_reason field MUST follow this structure:
1. **DEPLOYMENT_ERROR_CONTEXT**: Quote the exact deployment error message OR state 'No deployment error - functional difference identified'
2. **ERROR_CAUSE**: Explain the root cause of why this error/difference occurred
3. **MODULE_ROLE**: Describe how this specific module should handle the transformation
4. **SPECIFIC_ISSUE**: The exact issue this module should fix (not all issues in the statement)
5. **EXPECTED_FIX**: Specify what the module should do to transform its specific issue to match the AI-corrected format
6. **TRANSFORMATION_PATTERNS**: Provide complete, implementable patterns for this module's issue only - NO placeholders or abbreviations:
   - Pattern 1: Transform current format to AI-corrected format (current_pattern → ai_corrected_pattern)
   - Pattern 2: Handle whitespace variations to match AI-corrected format (current_with_spaces → ai_corrected_pattern)
   - Pattern 3: Handle case variations to match AI-corrected format (current_different_case → ai_corrected_pattern)
   - Pattern 4: Handle complex variations to match AI-corrected format (current_with_newlines → ai_corrected_pattern)
7. **IMPLEMENTATION_GUIDANCE**: Complete, actionable steps to fix this module's specific issue - NO vague descriptions:
   - Step 1: Identify current pattern that needs transformation for this module's specific issue
   - Step 2: Extract variable components from current output for this module's responsibility
   - Step 3: Apply transformation template to fix this module's specific issue only
   - Step 4: Validate result addresses this module's specific transformation requirement

**PATTERN COMPLETENESS REQUIREMENTS**:
- **COMPLETE PATTERNS**: Always provide complete, executable patterns - never use placeholders like (...), ..., or abbreviated syntax
- **FULL EXPRESSIONS**: Include complete expressions with all parameters and components
- **SPECIFIC SYNTAX**: Provide exact syntax that can be directly implemented without interpretation
- **NO ABBREVIATIONS**: Avoid shortened or truncated patterns - always show the complete structure
- **IMPLEMENTABLE GUIDANCE**: Ensure patterns can be directly used in module enhancement

**PATTERN QUALITY STANDARDS**:
❌ AVOID: Incomplete patterns with placeholders, ellipsis, or abbreviated syntax
✅ PROVIDE: Complete, specific patterns that include all necessary components and parameters
❌ AVOID: Generic or vague pattern descriptions
✅ PROVIDE: Exact syntax that matches the actual statement structure

**RESPONSIBILITY REASON VALIDATION**:
Before providing responsibility reasons, ensure:
1. **COMPLETE PATTERNS**: All transformation patterns include full syntax elements
2. **NO PLACEHOLDERS**: Never use (...), ..., or abbreviated representations
3. **IMPLEMENTABLE STEPS**: All implementation guidance is specific and actionable
4. **EXACT SYNTAX**: Use actual syntax from the statements being analyzed
5. **DIRECT USABILITY**: Patterns and guidance can be directly used without interpretation

**EXAMPLE RESPONSIBILITY_REASON FOR DEPLOYMENT ERROR**:
"DEPLOYMENT_ERROR_CONTEXT: syntax error at or near TABLE ERROR_CAUSE: TABLE(XMLSEQUENCE(...)) syntax not supported in PostgreSQL MODULE_ROLE: xml_sequence module handles XML sequence transformations SPECIFIC_ISSUE: Transform TABLE(XMLSEQUENCE(extract(...))) to PostgreSQL-compatible format EXPECTED_FIX: Convert TABLE(XMLSEQUENCE(...)) to UNNEST(xpath(...)) structure only TRANSFORMATION_PATTERNS: [Pattern 1: TABLE(XMLSEQUENCE(extract(...))) → UNNEST(xpath(...)), Pattern 2: Handle spacing variations] IMPLEMENTATION_GUIDANCE: [Step 1: Identify TABLE(XMLSEQUENCE patterns, Step 2: Transform to UNNEST(xpath format, Step 3: Preserve other statement parts unchanged]"

**EXAMPLE RESPONSIBILITY_REASON FOR FUNCTIONAL DIFFERENCE**:
"DEPLOYMENT_ERROR_CONTEXT: No deployment error - functional difference identified ERROR_CAUSE: CASE statement structure differs from AI-corrected simple SELECT format MODULE_ROLE: case_handling module simplifies CASE statements SPECIFIC_ISSUE: Transform CASE WHEN...THEN NULL ELSE...END to simple SELECT UNNEST format EXPECTED_FIX: Replace CASE statement with direct SELECT UNNEST(...) call only TRANSFORMATION_PATTERNS: [Pattern 1: CASE WHEN...THEN NULL ELSE...END → SELECT UNNEST(...), Pattern 2: Handle text casting variations] IMPLEMENTATION_GUIDANCE: [Step 1: Identify CASE statement patterns, Step 2: Extract UNNEST logic, Step 3: Replace with direct SELECT call]"

**MULTIPLE ISSUE HANDLING STRATEGY**:
When multiple transformation gaps exist in the same statement:
1. **PRIMARY**: Identify deployment error modules first (highest priority) - use exact keywords OR functional patterns
2. **SECONDARY**: Identify additional modules for remaining functional differences - consider similar transformation capabilities
3. **SPECIFIC**: Each module gets patterns for its specific issue only
4. **FLEXIBLE**: Consider modules that handle similar patterns even if keywords vary (e.g., xml_sequence for any XML processing)
5. **COMPREHENSIVE**: Ensure ALL transformation gaps are covered by identified modules



VALIDATION APPROACH:
==================
For each potential module, verify:
1. **DEPLOYMENT ERROR MODULES**: Is this module's PRIMARY purpose to handle the failing syntax element OR similar functional patterns?
2. **DEPLOYMENT ERROR MODULES**: Does the deployment error relate to this module's conversion logic (exact keywords OR functional equivalents)?
3. **DEPLOYMENT ERROR MODULES**: Would fixing this module resolve the specific deployment error (even with keyword variations)?
4. **TRANSFORMATION GAP MODULES**: Can this module handle specific differences between current and AI-corrected output?
5. **TRANSFORMATION GAP MODULES**: Is this module needed to achieve complete AI-corrected format (including functional variations)?
6. **ALL MODULES**: Does the responsibility_reason clearly explain the specific issue this module should fix?
7. **FLEXIBLE MATCHING**: Consider modules that handle similar transformations even if exact keywords don't match

**Key Principles**:
- **COMPREHENSIVE**: Identify modules for ALL transformation gaps, not just deployment errors
- **SPECIFIC**: Each module gets responsibility for its specific issue only
- **FLEXIBLE**: Consider modules that handle similar patterns even if exact keywords don't match
- **FUNCTIONAL**: Focus on transformation capabilities rather than strict keyword matching
- **COMPLETE**: Ensure identified modules can achieve complete AI-corrected output transformation

**DEPLOYMENT ERROR FOCUS**: Every responsibility_reason MUST reference the specific deployment error and explain how the module caused it.

TRANSFORMATION PATTERN QUALITY REQUIREMENTS:
===========================================
Ensure each responsibility_reason provides:
1. **AI-CORRECTED TARGET PATTERNS**: Specific transformation examples to match the exact AI-corrected output format
2. **VARIATION COVERAGE**: Handle whitespace, case, bracket, and formatting differences
3. **FORMAT MATCHING**: Handle transformation from current to AI-corrected structure
4. **FLEXIBLE REGEX**: Use patterns like \\s*, \\s+, re.IGNORECASE for robust matching to AI-corrected format
5. **TARGET-FOCUSED APPROACH**: Make patterns specifically aimed at achieving the AI-corrected output and adaptable to similar constructs without hardcoding specific keywords
6. **IMPLEMENTATION READY**: Provide step-by-step guidance to achieve the AI-corrected format
7. **MULTIPLE SCENARIOS**: Include 3-4 pattern variations to handle different current formats while targeting AI-corrected output
8. **VALIDATION CRITERIA**: Specify how to verify the transformation matches the AI-corrected output exactly

CRITICAL SUCCESS FACTORS:
========================
- **AI-CORRECTED OUTPUT FOCUS**: Use the AI-corrected output as the definitive target for all transformations
- **TARGET-SPECIFIC PATTERNS**: Create patterns that specifically transform current output to AI-corrected format
- **CLEAR GUIDANCE**: Provide specific implementation steps to achieve the AI-corrected output
- **TESTABLE PATTERNS**: Include validation criteria to verify transformation matches AI-corrected output exactly
- **FORMAT-DRIVEN APPROACH**: Focus on transforming to the exact AI-corrected output structure and format

Focus ONLY on identifying responsible modules. Do NOT suggest code changes.
"""
    
    return prompt